import os
from typing import List, Optional
import pandas as pd
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage, BaseMessage
from langchain.agents.agent_types import AgentType
from langchain_experimental.agents.agent_toolkits import create_pandas_dataframe_agent





def get_env(name: str, default: Optional[str] = None) -> Optional[str]:
	"""只获取环境变量，适合 Docker 环境。"""
	return os.environ.get(name, default)


# def build_messages() -> List[BaseMessage]:
# 	return [
# 		SystemMessage(content="你是一个善于总结国际形势的助手。"),	
# 		HumanMessage(content="请帮我给出当大国之间关系的走向："),	
# 	]


def main() -> None:
	# 改为读取系统/用户级环境变量（Windows 注册表），仍兼容进程变量
	base_url = get_env(
		"DASHSCOPE_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1"
	)
	api_key = (
		get_env("DASHSCOPE_API_KEY")
		# or get_env("ALIYUN_DASHSCOPE_API_KEY")
		# or get_env("OPENAI_API_KEY")
	)

	if not api_key:
		print(
			"未检测到 API Key。请先设置环境变量 DASHSCOPE_API_KEY=你的DashScope密钥 再运行。"
		)
		print(
			"示例(当前会话生效，PowerShell)：$env:DASHSCOPE_API_KEY=\"sk-xxxxx\""
		)
		return

	model = get_env("QWEN_MODEL", "qwen-max")

	# 智能读取 CSV：自动尝试常见编码，避免 UnicodeDecodeError
	def read_csv_smart(path: str) -> pd.DataFrame:
		encodings = [
			"utf-8",
			"utf-8-sig",
			"gbk",
			"gb18030",
			"cp936",
			"latin1",
		]
		last_err: Exception | None = None
		for enc in encodings:
			try:
				return pd.read_csv(path, encoding=enc)
			except Exception as e:  # 尝试下一个编码
				last_err = e
				continue
		raise last_err if last_err else RuntimeError("无法读取 CSV：未知错误")

	df = read_csv_smart("./data/1.csv").fillna(value="未知")
	print(df.head(10))

	chat = ChatOpenAI(
		model=model,
		api_key=api_key,
		base_url=base_url,
		# 可按需设置：temperature=0.7, timeout=60
	)
	CSV_PROMPT_PREFIX = """
	First set the pandas display options to show all the columns,
	get the column names, then answer the question.
	"""

	CSV_PROMPT_SUFFIX = """
	- **ALWAYS** before giving the Final Answer, try another method.
	Then reflect on the answers of the two methods you did and ask yourself
	if it answers correctly the original question.
	If you are not sure, try another method.
	- If the methods tried do not give the same result,reflect and
	try again until you have two methods that have the same result.
	- If you still cannot arrive to a consistent result, say that
	you are not sure of the answer.
	- If you are sure of the correct answer, create a beautiful
	and thorough response using Markdown.
	- **DO NOT MAKE UP AN ANSWER OR USE PRIOR KNOWLEDGE,
	ONLY USE THE RESULTS OF THE CALCULATIONS YOU HAVE DONE**.
	- **ALWAYS**, as part of your "Final Answer", explain how you got
	to the answer on a section that starts with: "\n\nExplanation:\n".
	In the explanation, mention the column names that you used to get
	to the final answer.
	"""
	QUESTION = "根据这些数值，你认为这是一个什么样的表格？在快超时前，请尽可能给出你的答案。"
	agent = create_pandas_dataframe_agent(llm=chat, df=df, allow_dangerous_code=True, verbose=True)

	# resp = agent.invoke("这个表没有表头，你算算一共多少行数据？注意，python是从0开始计数的，所以行数应该是代码给出的数字加1。")
	resp = agent.invoke(CSV_PROMPT_PREFIX + QUESTION + CSV_PROMPT_SUFFIX)
	# messages = build_messages()
	# resp = chat.invoke(messages)
	print("模型回复:\n" + resp["output"])



if __name__ == "__main__":
	main()

