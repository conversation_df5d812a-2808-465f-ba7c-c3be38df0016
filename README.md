# LangChain 调用 Qwen-Max 最小示例

本示例展示如何使用 LangChain 通过 DashScope 的 OpenAI 兼容接口调用通义千问 Qwen-Max。

## 准备

1. 安装依赖

```powershell
# 在项目根目录执行
pip install -r requirements.txt
```

2. 配置环境变量（PowerShell）

```powershell
$env:DASHSCOPE_API_KEY="你的DashScope密钥"
# 可选：覆盖默认地址
# $env:DASHSCOPE_BASE_URL="https://dashscope.aliyuncs.com/compatible-mode/v1"
# 可选：切换模型
# $env:QWEN_MODEL="qwen-max"
```

## 运行

```powershell
python .\fstagent.py
```

看到“模型回复:”即表示成功。
